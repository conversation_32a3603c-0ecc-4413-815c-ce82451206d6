import asyncio
import logging
import sys
from os import getenv

from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, F
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.filters import CommandStart, Command
from aiogram.types import Message
from aiogram.webhook.aiohttp_server import <PERSON>R<PERSON><PERSON><PERSON><PERSON><PERSON>, setup_application
from aiohttp import web
from dotenv import load_dotenv
from database import init_database, close_database
from common.handlers import router as common_router
from common.register_handlers_and_transitions import register_handlers
from manager.handlers.main import show_manager_main_menu
from manager.handlers import router as manager_router
from student.handlers import router as student_router
from student.handlers.main import show_student_main_menu
from curator.handlers import router as curator_router
from curator.handlers.main import show_curator_main_menu
from teacher.handlers import router as teacher_router
from teacher.handlers.main import show_teacher_main_menu
from admin.handlers import router as admin_router
from admin.handlers.main import show_admin_main_menu
from middlewares.role_middleware import RoleMiddleware

load_dotenv()

TOKEN = getenv("BOT_TOKEN")
WEBHOOK_MODE = getenv("WEBHOOK_MODE", "false").lower() == "true"
WEBHOOK_HOST = getenv("WEBHOOK_HOST", "https://your-domain.com")
WEBHOOK_PATH = getenv("WEBHOOK_PATH", "/webhook")
WEBHOOK_URL = f"{WEBHOOK_HOST}{WEBHOOK_PATH}"
WEB_SERVER_HOST = getenv("WEB_SERVER_HOST", "0.0.0.0")
WEB_SERVER_PORT = int(getenv("WEB_SERVER_PORT", "8000"))

async def start_command(message: Message, user_role: str):
    """Обработчик команды /start, перенаправляющий на соответствующие функции"""
    if user_role == "admin":
        await show_admin_main_menu(message)
    elif user_role == "manager":
        await show_manager_main_menu(message)
    elif user_role == "curator":
        await show_curator_main_menu(message)
    elif user_role == "teacher":
        await show_teacher_main_menu(message)
    else:  # По умолчанию считаем пользователя студентом
        await show_student_main_menu(message)

async def curator_command(message: Message):
    """Обработчик команды /curator, открывающий меню куратора"""
    await show_curator_main_menu(message)

async def teacher_command(message: Message):
    """Обработчик команды /teacher, открывающий меню преподавателя"""
    await show_teacher_main_menu(message)

async def on_startup(bot: Bot) -> None:
    """Действия при запуске бота"""
    try:
        # Инициализируем подключение к базе данных
        await init_database()
        logging.info("✅ База данных инициализирована")
    except Exception as e:
        logging.error(f"❌ Ошибка инициализации базы данных: {e}")
        raise

    # Устанавливаем команды бота
    from aiogram.types import BotCommand
    commands = [
        BotCommand(command="start", description="Запустить бота"),
        BotCommand(command="admin", description="Панель администратора"),
        BotCommand(command="curator", description="Меню куратора"),
        BotCommand(command="teacher", description="Меню преподавателя"),
        BotCommand(command="manager", description="Меню менеджера"),
        BotCommand(command="student", description="Меню студента")
    ]

    try:
        await bot.set_my_commands(commands)
        logging.info("✅ Команды бота установлены")
    except Exception as e:
        logging.error(f"❌ Ошибка установки команд: {e}")

    if WEBHOOK_MODE:
        # Устанавливаем webhook
        try:
            await bot.set_webhook(url=WEBHOOK_URL, drop_pending_updates=True)
            logging.info(f"✅ Webhook установлен: {WEBHOOK_URL}")
        except Exception as e:
            logging.error(f"❌ Ошибка установки webhook: {e}")
            raise
    else:
        # Удаляем webhook для polling режима
        try:
            await bot.delete_webhook(drop_pending_updates=True)
            logging.info("✅ Webhook удален, используется polling")
        except Exception as e:
            logging.error(f"❌ Ошибка удаления webhook: {e}")

async def on_shutdown(bot: Bot) -> None:
    """Действия при остановке бота"""
    try:
        await close_database()
        logging.info("✅ База данных отключена")
    except Exception as e:
        logging.error(f"❌ Ошибка отключения БД: {e}")

    if WEBHOOK_MODE:
        try:
            await bot.delete_webhook()
            logging.info("✅ Webhook удален")
        except Exception as e:
            logging.error(f"❌ Ошибка удаления webhook: {e}")

async def health_check(request):
    """Healthcheck эндпоинт"""
    return web.Response(text="OK")

async def main() -> None:
    bot = Bot(token=TOKEN, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
    dp = Dispatcher()

    # Регистрируем startup и shutdown хуки
    dp.startup.register(lambda: on_startup(bot))
    dp.shutdown.register(lambda: on_shutdown(bot))

    # Регистрируем middleware для определения роли пользователя
    dp.message.middleware(RoleMiddleware())
    dp.callback_query.middleware(RoleMiddleware())
    
    # Регистрируем обработчик команды /start
    dp.message.register(start_command, CommandStart())
    
    # Регистрируем обработчик команды /curator
    dp.message.register(curator_command, Command("curator"))
    
    # Регистрируем обработчик команды /teacher
    dp.message.register(teacher_command, Command("teacher"))
    
    # Регистрируем обработчик команды /student
    dp.message.register(show_student_main_menu, Command("student"))

    dp.message.register(show_manager_main_menu, Command("manager"))

    # Регистрируем обработчик команды /admin
    dp.message.register(show_admin_main_menu, Command("admin"))

    # Команды устанавливаются в on_startup хуке

    # Включаем роутеры для разных ролей
    dp.include_router(common_router)
    dp.include_router(admin_router)
    dp.include_router(student_router)
    dp.include_router(teacher_router)
    dp.include_router(curator_router)
    dp.include_router(manager_router)
    register_handlers()

    if WEBHOOK_MODE:
        # Webhook режим с aiohttp сервером
        app = web.Application()
        app.router.add_get("/health", health_check)

        # Настраиваем webhook handler
        webhook_requests_handler = SimpleRequestHandler(
            dispatcher=dp,
            bot=bot,
        )
        webhook_requests_handler.register(app, path=WEBHOOK_PATH)
        setup_application(app, dp, bot=bot)

        # Запускаем веб-сервер
        logging.info(f"Запуск webhook сервера на {WEB_SERVER_HOST}:{WEB_SERVER_PORT}")
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, WEB_SERVER_HOST, WEB_SERVER_PORT)
        await site.start()

        # Ждем сигнал завершения
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logging.info("🛑 Получен сигнал завершения")
        finally:
            await runner.cleanup()
    else:
        # Polling режим
        logging.info("Запуск в polling режиме")
        await dp.start_polling(bot)

def setup_logging():
    """Настройка логирования в файл и консоль"""
    import os
    from datetime import datetime

    # Создаем папку для логов если не существует
    os.makedirs("logs", exist_ok=True)

    # Формат логов
    log_format = "%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"

    # Настраиваем корневой логгер
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Очищаем существующие обработчики
    logger.handlers.clear()

    # 1. Обработчик для консоли (цветной)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter(
        "%(asctime)s | \033[%(color)s%(levelname)-8s\033[0m | %(name)-20s | %(message)s",
        datefmt=date_format
    )

    # Добавляем цвета для разных уровней
    class ColoredFormatter(logging.Formatter):
        COLORS = {
            'DEBUG': '36m',    # Cyan
            'INFO': '32m',     # Green
            'WARNING': '33m',  # Yellow
            'ERROR': '31m',    # Red
            'CRITICAL': '35m'  # Magenta
        }

        def format(self, record):
            record.color = self.COLORS.get(record.levelname, '37m')  # Default white
            return super().format(record)

    console_handler.setFormatter(ColoredFormatter(
        "%(asctime)s | \033[%(color)s%(levelname)-8s\033[0m | %(name)-20s | %(message)s",
        datefmt=date_format
    ))
    logger.addHandler(console_handler)

    # 2. Обработчик для файла (все логи)
    today = datetime.now().strftime("%Y-%m-%d")
    file_handler = logging.FileHandler(f"logs/bot_{today}.log", encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter(log_format, datefmt=date_format))
    logger.addHandler(file_handler)

    # 3. Обработчик для файла ошибок
    error_handler = logging.FileHandler(f"logs/errors_{today}.log", encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(logging.Formatter(log_format, datefmt=date_format))
    logger.addHandler(error_handler)

    # Логируем старт
    logging.info("🚀 Система логирования настроена")
    logging.info(f"📁 Логи сохраняются в: logs/bot_{today}.log")
    logging.info(f"❌ Ошибки сохраняются в: logs/errors_{today}.log")

if __name__ == "__main__":
    setup_logging()
    asyncio.run(main())
