#!/bin/bash

echo "🧪 Локальное тестирование телеграм бота"
echo "======================================="

# Проверяем Python
echo "🐍 Проверка Python..."
if command -v python3 &> /dev/null; then
    python3 --version
else
    echo "❌ Python3 не установлен"
    exit 1
fi

# Проверяем pip
echo "📦 Проверка pip..."
if command -v pip3 &> /dev/null; then
    pip3 --version
else
    echo "❌ pip3 не установлен"
    exit 1
fi

# Создаем виртуальное окружение
echo "🔧 Создание виртуального окружения..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ Виртуальное окружение создано"
else
    echo "✅ Виртуальное окружение уже существует"
fi

# Активируем виртуальное окружение
echo "🔄 Активация виртуального окружения..."
source venv/bin/activate || source venv/Scripts/activate

# Устанавливаем зависимости
echo "📥 Установка зависимостей..."
pip install -r requirements.txt

# Проверяем наличие .env файла
if [ ! -f ".env" ]; then
    echo "📝 Создание .env файла для тестирования..."
    cp .env.production .env
    echo ""
    echo "⚠️  ВАЖНО: Отредактируйте файл .env и добавьте:"
    echo "   - BOT_TOKEN (получите у @BotFather)"
    echo "   - Остальные переменные можно оставить как есть для тестирования"
    echo ""
    echo "📝 Откройте файл .env в редакторе:"
    echo "   nano .env"
    echo ""
    read -p "Нажмите Enter после редактирования .env файла..."
fi

# Проверяем токен бота
echo "🔍 Проверка конфигурации..."
source .env
if [ -z "$BOT_TOKEN" ] || [ "$BOT_TOKEN" = "your_bot_token_here" ]; then
    echo "❌ BOT_TOKEN не настроен в .env файле"
    echo "📝 Получите токен у @BotFather и добавьте в .env"
    exit 1
fi

echo "✅ Конфигурация проверена"

# Запускаем бота без базы данных (с временными данными)
echo ""
echo "🚀 Запуск бота в тестовом режиме..."
echo "   (используются временные данные в памяти)"
echo ""
echo "Для остановки нажмите Ctrl+C"
echo ""

python3 main.py
