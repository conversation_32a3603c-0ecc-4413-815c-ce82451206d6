version: '3.8'

secrets:
  bot_token:
    external: true
  postgres_password:
    external: true

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres
    environment:
      POSTGRES_DB: telebot
      POSTGRES_USER: telebot_user
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - telebot_network

  bot:
    build: .
    container_name: telebot_app
    environment:
      BOT_TOKEN_FILE: /run/secrets/bot_token
      DATABASE_URL: postgresql://telebot_user@postgres:5432/telebot
    secrets:
      - bot_token
      - postgres_password
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - telebot_network
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:

networks:
  telebot_network:
    driver: bridge

secrets:
  bot_token:
    file: /etc/telebot/secrets/bot_token.txt
  postgres_password:
    file: /etc/telebot/secrets/postgres_password.txt
