# Пример конфигурации nginx для телеграм бота (если понадобится webhook)

server {
    listen 80;
    server_name your-domain.com;

    # Webhook для телеграм бота
    location /webhook {
        proxy_pass http://localhost:8000;  # Порт контейнера бота
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Админ панель (если добавите веб-интерфейс)
    location /admin {
        proxy_pass http://localhost:8001;  # Порт веб-админки
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API эндпоинты (если добавите)
    location /api {
        proxy_pass http://localhost:8002;  # Порт API
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Статические файлы (если понадобятся)
    location /static {
        alias /var/www/telebot/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS версия (рекомендуется для webhook)
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    location /webhook {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
