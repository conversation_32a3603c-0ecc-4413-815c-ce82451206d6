#!/bin/bash

# Скрипт для безопасной настройки переменных окружения на сервере

echo "🔐 Настройка переменных окружения для телеграм бота..."

# Создаем защищенную директорию для конфигов
sudo mkdir -p /etc/telebot
sudo chmod 700 /etc/telebot

# Создаем файл с переменными окружения
sudo tee /etc/telebot/env > /dev/null << EOF
# Telegram Bot Configuration
BOT_TOKEN=your_bot_token_here
POSTGRES_DB=telebot
POSTGRES_USER=telebot_user
POSTGRES_PASSWORD=your_secure_password_here
DATABASE_URL=*****************************************************************/telebot
ENVIRONMENT=production
EOF

# Устанавливаем безопасные права доступа
sudo chmod 600 /etc/telebot/env
sudo chown root:root /etc/telebot/env

echo "✅ Файл конфигурации создан: /etc/telebot/env"
echo "📝 Отредактируйте файл и добавьте реальные значения:"
echo "sudo nano /etc/telebot/env"
