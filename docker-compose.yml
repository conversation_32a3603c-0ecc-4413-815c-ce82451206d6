version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-telebot}
      POSTGRES_USER: ${POSTGRES_USER:-telebot_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - telebot_network

  bot:
    build: .
    container_name: telebot_app
    env_file:
      - /etc/telebot/env  # Безопасное хранение переменных
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - telebot_network
    volumes:
      - ./logs:/app/logs
      - /etc/telebot/env:/etc/telebot/env:ro  # Монтируем файл только для чтения

volumes:
  postgres_data:

networks:
  telebot_network:
    driver: bridge
