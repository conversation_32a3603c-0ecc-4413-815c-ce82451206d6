version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-telebot}
      POSTGRES_USER: ${POSTGRES_USER:-telebot_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    # ports:
    #   - "5432:5432"  # Закрыт для безопасности, доступ только внутри сети
    restart: unless-stopped
    networks:
      - telebot_network

  nginx:
    image: nginx:alpine
    container_name: telebot_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      bot:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - telebot_network

  bot:
    build: .
    container_name: telebot_app
    env_file:
      - /etc/telebot/env  # Все переменные берутся из безопасного файла
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - telebot_network
    volumes:
      - ./logs:/app/logs
    expose:
      - "8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  telebot_network:
    driver: bridge
