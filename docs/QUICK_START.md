# Быстрый старт деплоя на Beget

## 1. Подготовка файлов

1. Скопируйте все файлы проекта на сервер Beget
2. Настройте безопасное хранение переменных окружения:

```bash
# Настройка переменных окружения
chmod +x scripts/setup_env.sh
sudo ./scripts/setup_env.sh

# Редактирование переменных
sudo nano /etc/telebot/env
```

Обязательно заполните:
- `BOT_TOKEN` - токен от @BotFather
- `POSTGRES_PASSWORD` - надежный пароль для базы данных
- `WEBHOOK_HOST` - ваш домен (например: https://bot.example.com)

## 2. Настройка SSL (для максимальной скорости)

```bash
# Настраиваем SSL сертификат
chmod +x scripts/setup_ssl.sh
sudo ./scripts/setup_ssl.sh
```

## 3. Запуск

```bash
# Делаем скрипт исполняемым
chmod +x deploy.sh

# Запускаем деплой
./deploy.sh
```

## 4. Проверка

```bash
# Проверяем статус контейнеров
docker-compose ps

# Смотрим логи бота
docker-compose logs -f bot
```

## 4. Управление

```bash
# Остановка
docker-compose down

# Перезапуск
docker-compose restart

# Обновление кода
git pull && docker-compose down && docker-compose build --no-cache && docker-compose up -d
```

## 5. Первоначальная настройка

1. Найдите свой Telegram ID (напишите боту `/start`)
2. Добавьте свой ID в `admin_ids` в файле `middlewares/role_middleware.py`
3. Перезапустите бота: `docker-compose restart bot`
4. Теперь у вас есть доступ к админ-панели через команду `/admin`

## Готово! 🚀

Ваш бот запущен и готов к работе. Полная документация в файле `DEPLOY_GUIDE.md`.
