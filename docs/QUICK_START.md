# Быстрый старт деплоя на Beget

## 1. Подготовка файлов

1. Скопируйте все файлы проекта на сервер Beget
2. Скопируйте `.env.production` в `.env` и заполните переменные:

```bash
cp .env.production .env
nano .env
```

Обязательно заполните:
- `BOT_TOKEN` - токен от @BotFather
- `POSTGRES_PASSWORD` - надежный пароль для базы данных

## 2. Запуск

```bash
# Делаем скрипт исполняемым
chmod +x deploy.sh

# Запускаем деплой
./deploy.sh
```

## 3. Проверка

```bash
# Проверяем статус контейнеров
docker-compose ps

# Смотрим логи бота
docker-compose logs -f bot
```

## 4. Управление

```bash
# Остановка
docker-compose down

# Перезапуск
docker-compose restart

# Обновление кода
git pull && docker-compose down && docker-compose build --no-cache && docker-compose up -d
```

## 5. Первоначальная настройка

1. Найдите свой Telegram ID (напишите боту `/start`)
2. Добавьте свой ID в `admin_ids` в файле `middlewares/role_middleware.py`
3. Перезапустите бота: `docker-compose restart bot`
4. Теперь у вас есть доступ к админ-панели через команду `/admin`

## Готово! 🚀

Ваш бот запущен и готов к работе. Полная документация в файле `DEPLOY_GUIDE.md`.
