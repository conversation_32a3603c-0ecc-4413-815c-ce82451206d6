2025-06-08 14:40:45 | INFO     | root                 | 🚀 Система логирования настроена
2025-06-08 14:40:45 | INFO     | root                 | 📁 Логи сохраняются в: logs/bot_2025-06-08.log
2025-06-08 14:40:45 | INFO     | root                 | ❌ Ошибки сохраняются в: logs/errors_2025-06-08.log
2025-06-08 14:40:45 | INFO     | root                 | 🚀 Запуск в polling режиме
2025-06-08 14:40:45 | INFO     | aiogram.dispatcher   | Start polling
2025-06-08 14:40:48 | INFO     | aiogram.dispatcher   | Run polling for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 14:41:11 | INFO     | aiogram.event        | Update id=829911494 is handled. Duration 2608 ms by bot id=7999611456
2025-06-08 14:41:16 | INFO     | aiogram.event        | Update id=829911495 is handled. Duration 1342 ms by bot id=7999611456
2025-06-08 14:41:18 | INFO     | aiogram.event        | Update id=829911496 is handled. Duration 984 ms by bot id=7999611456
2025-06-08 14:41:21 | INFO     | aiogram.event        | Update id=829911497 is handled. Duration 719 ms by bot id=7999611456
2025-06-08 14:41:31 | INFO     | aiogram.event        | Update id=829911498 is handled. Duration 1328 ms by bot id=7999611456
2025-06-08 14:41:37 | INFO     | aiogram.event        | Update id=829911499 is handled. Duration 1359 ms by bot id=7999611456
2025-06-08 14:41:49 | INFO     | aiogram.event        | Update id=829911500 is handled. Duration 1984 ms by bot id=7999611456
2025-06-08 14:51:47 | INFO     | aiogram.dispatcher   | Polling stopped
2025-06-08 14:51:47 | INFO     | aiogram.dispatcher   | Polling stopped for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 14:52:20 | INFO     | root                 | 🚀 Система логирования настроена
2025-06-08 14:52:20 | INFO     | root                 | 📁 Логи сохраняются в: logs/bot_2025-06-08.log
2025-06-08 14:52:20 | INFO     | root                 | ❌ Ошибки сохраняются в: logs/errors_2025-06-08.log
2025-06-08 14:52:20 | INFO     | root                 | 🚀 Запуск в polling режиме
2025-06-08 14:52:20 | INFO     | aiogram.dispatcher   | Start polling
2025-06-08 14:52:23 | INFO     | aiogram.dispatcher   | Run polling for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 14:52:29 | INFO     | aiogram.dispatcher   | Polling stopped
2025-06-08 14:52:29 | INFO     | aiogram.dispatcher   | Polling stopped for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 14:55:48 | INFO     | root                 | 🚀 Система логирования настроена
2025-06-08 14:55:48 | INFO     | root                 | 📁 Логи сохраняются в: logs/bot_2025-06-08.log
2025-06-08 14:55:48 | INFO     | root                 | ❌ Ошибки сохраняются в: logs/errors_2025-06-08.log
2025-06-08 14:55:49 | INFO     | root                 | 🚀 Запуск в polling режиме
2025-06-08 14:55:49 | INFO     | aiogram.dispatcher   | Start polling
2025-06-08 14:55:51 | INFO     | aiogram.dispatcher   | Run polling for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 14:56:09 | INFO     | aiogram.event        | Update id=829911501 is handled. Duration 2530 ms by bot id=7999611456
2025-06-08 14:56:14 | INFO     | aiogram.event        | Update id=829911502 is handled. Duration 1452 ms by bot id=7999611456
2025-06-08 14:56:17 | INFO     | aiogram.event        | Update id=829911503 is handled. Duration 1375 ms by bot id=7999611456
2025-06-08 14:56:19 | INFO     | aiogram.event        | Update id=829911504 is handled. Duration 734 ms by bot id=7999611456
2025-06-08 14:56:23 | INFO     | aiogram.event        | Update id=829911505 is handled. Duration 1327 ms by bot id=7999611456
2025-06-08 14:56:26 | INFO     | aiogram.event        | Update id=829911506 is handled. Duration 1968 ms by bot id=7999611456
2025-06-08 14:56:30 | INFO     | aiogram.event        | Update id=829911507 is handled. Duration 968 ms by bot id=7999611456
2025-06-08 14:56:34 | INFO     | aiogram.event        | Update id=829911508 is handled. Duration 2297 ms by bot id=7999611456
2025-06-08 14:56:39 | INFO     | aiogram.event        | Update id=829911509 is handled. Duration 656 ms by bot id=7999611456
2025-06-08 14:56:41 | INFO     | aiogram.event        | Update id=829911510 is handled. Duration 842 ms by bot id=7999611456
2025-06-08 14:56:44 | INFO     | aiogram.event        | Update id=829911511 is handled. Duration 844 ms by bot id=7999611456
2025-06-08 14:56:48 | INFO     | aiogram.event        | Update id=829911512 is handled. Duration 905 ms by bot id=7999611456
2025-06-08 14:56:52 | INFO     | aiogram.event        | Update id=829911513 is handled. Duration 1157 ms by bot id=7999611456
2025-06-08 14:56:55 | INFO     | aiogram.event        | Update id=829911514 is not handled. Duration 0 ms by bot id=7999611456
2025-06-08 14:56:55 | ERROR    | aiogram.event        | Cause exception while process update id=829911514 by bot id=7999611456
AttributeError: 'FSMContext' object has no attribute 'course_id'
Traceback (most recent call last):
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\middlewares\error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\middlewares\user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\fsm\middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\event\telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\event\handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\event\telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\middlewares\role_middleware.py", line 36, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\.venv\Lib\site-packages\aiogram\dispatcher\event\handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "C:\1important\Python Projects\telebot\common\handlers.py", line 12, in go_back
    await navigation_manager.handle_back(callback, state, user_role)
  File "C:\1important\Python Projects\telebot\common\navigation.py", line 66, in handle_back
    await handler(callback, state, user_role)
  File "C:\1important\Python Projects\telebot\manager\handlers\lessons.py", line 59, in process_view_action
    if callback_data.course_id is not None and callback_data.subject_id is None:
       ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'FSMContext' object has no attribute 'course_id'
2025-06-08 15:15:52 | INFO     | aiogram.dispatcher   | Polling stopped
2025-06-08 15:15:53 | INFO     | aiogram.dispatcher   | Polling stopped for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 15:22:38 | INFO     | root                 | 🚀 Система логирования настроена
2025-06-08 15:22:38 | INFO     | root                 | 📁 Логи сохраняются в: logs/bot_2025-06-08.log
2025-06-08 15:22:38 | INFO     | root                 | ❌ Ошибки сохраняются в: logs/errors_2025-06-08.log
2025-06-08 15:22:39 | INFO     | root                 | 🚀 Запуск в polling режиме
2025-06-08 15:22:39 | INFO     | aiogram.dispatcher   | Start polling
2025-06-08 15:22:42 | INFO     | aiogram.dispatcher   | Run polling for bot @ankv123_bot id=7999611456 - 'ankv123'
2025-06-08 15:23:18 | INFO     | aiogram.event        | Update id=829911515 is handled. Duration 3437 ms by bot id=7999611456
2025-06-08 15:23:34 | INFO     | aiogram.event        | Update id=829911516 is handled. Duration 1593 ms by bot id=7999611456
2025-06-08 15:23:42 | INFO     | aiogram.event        | Update id=829911517 is handled. Duration 1280 ms by bot id=7999611456
2025-06-08 15:23:45 | INFO     | aiogram.event        | Update id=829911518 is handled. Duration 967 ms by bot id=7999611456
2025-06-08 15:23:48 | INFO     | aiogram.event        | Update id=829911519 is handled. Duration 702 ms by bot id=7999611456
2025-06-08 15:23:51 | INFO     | aiogram.event        | Update id=829911520 is handled. Duration 1187 ms by bot id=7999611456
2025-06-08 15:23:53 | INFO     | aiogram.event        | Update id=829911521 is handled. Duration 640 ms by bot id=7999611456
2025-06-08 15:23:57 | INFO     | aiogram.event        | Update id=829911522 is handled. Duration 1297 ms by bot id=7999611456
2025-06-08 15:23:59 | INFO     | aiogram.event        | Update id=829911523 is handled. Duration 625 ms by bot id=7999611456
2025-06-08 15:24:02 | INFO     | aiogram.event        | Update id=829911524 is handled. Duration 954 ms by bot id=7999611456
2025-06-08 15:24:04 | INFO     | aiogram.event        | Update id=829911525 is handled. Duration 891 ms by bot id=7999611456
2025-06-08 15:24:07 | INFO     | aiogram.event        | Update id=829911526 is handled. Duration 921 ms by bot id=7999611456
2025-06-08 15:24:11 | INFO     | aiogram.event        | Update id=829911527 is handled. Duration 1265 ms by bot id=7999611456
2025-06-08 15:24:13 | INFO     | aiogram.event        | Update id=829911528 is handled. Duration 1077 ms by bot id=7999611456
2025-06-08 15:24:16 | INFO     | aiogram.event        | Update id=829911529 is handled. Duration 1625 ms by bot id=7999611456
2025-06-08 15:24:21 | INFO     | manager.handlers.homework | Вызван обработчик show_homework_management
2025-06-08 15:24:22 | INFO     | aiogram.event        | Update id=829911530 is handled. Duration 905 ms by bot id=7999611456
2025-06-08 15:30:21 | INFO     | aiogram.event        | Update id=829911531 is handled. Duration 1906 ms by bot id=7999611456
2025-06-08 15:36:13 | INFO     | aiogram.dispatcher   | Polling stopped
2025-06-08 15:36:13 | INFO     | aiogram.dispatcher   | Polling stopped for bot @ankv123_bot id=7999611456 - 'ankv123'
